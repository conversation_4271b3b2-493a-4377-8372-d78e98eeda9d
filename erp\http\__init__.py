"""
HTTP routing system for ERP
Provides Odoo-style @http.route() decorator with JSON RPC 2.0 support
"""

from .route import route, systemRoute, SystemRouteRegistry, get_system_route_registry, setup_http_routes, RouteType, HttpMethod
from .jsonrpc import <PERSON>sonRpc<PERSON>and<PERSON>, JsonRpcError, JsonRpcRequest, JsonRpcResponse
from .auth import AuthType, require_auth
from .cors import cors_handler
from .controller import Controller, ControllerRegistry, get_controller_registry

__all__ = [
    'route',
    'systemRoute',
    'SystemRouteRegistry',
    'get_system_route_registry',
    'setup_http_routes',
    'RouteType',
    'HttpMethod',
    '<PERSON>sonRpcHandler',
    'JsonRpcError',
    'JsonRpcRequest',
    'JsonRpcResponse',
    'AuthType',
    'require_auth',
    'cors_handler',
    'Controller',
    'ControllerRegistry',
    'get_controller_registry'
]
